"""
Scraper utilities for Nashira Wine Scraper
Integrates functionality from abcd.py, business_scraper.py, and google_maps_scraper.py
From Daru to Dolce Vita
"""

import csv
import requests
import asyncio
import aiohttp
import time
import re
import os
import hashlib
import json
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs, unquote, quote
from django.conf import settings
from django.core.cache import cache
import logging

logger = logging.getLogger('nashira')

# Configuration from settings
MAX_WORKERS = getattr(settings, 'SCRAPER_CONFIG', {}).get('MAX_WORKERS', 3)
REQUEST_TIMEOUT = getattr(settings, 'SCRAPER_CONFIG', {}).get('REQUEST_TIMEOUT', 20)
CACHE_TIMEOUT = getattr(settings, 'SCRAPER_CONFIG', {}).get('CACHE_TIMEOUT', 86400)

# URLs and headers
DUCKDUCKGO_BASE = 'https://html.duckduckgo.com/html/?q={}'
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5"
}

# Social media platforms to detect
SOCIAL_PLATFORMS = ['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'tiktok', 'x.com']


def clean_url(url: str) -> Optional[str]:
    """
    Cleans and normalizes URLs, including removing tracking parameters
    and handling redirects.
    """
    if not url or not isinstance(url, str):
        return None

    url = url.strip()
    if not url:
        return None

    # Handle DuckDuckGo redirect URLs
    if "uddg=" in url and "duckduckgo.com" in urlparse(url).netloc:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        real_url = query.get("uddg", [None])[0]
        if real_url:
            real_url = unquote(real_url)
            if not urlparse(real_url).scheme:
                real_url = "https://" + real_url.lstrip('/')
            return real_url.strip()

    # Handle Google redirect URLs
    elif "url=" in url and "google" in urlparse(url).netloc:
        parsed = urlparse(url)
        query = parse_qs(parsed.query)
        real_url = query.get("url", [None])[0] or query.get("q", [None])[0]
        if real_url:
            real_url = unquote(real_url)
            if not urlparse(real_url).scheme:
                real_url = "https://" + real_url.lstrip('/')
            return real_url.strip()

    # Handle protocol-relative URLs (starting with //)
    elif url.startswith("//"):
        return "https:" + url

    # Add scheme if missing
    elif not urlparse(url).scheme:
        return "https://" + url.lstrip('/')

    return url


def extract_phone(text: str) -> str:
    """
    Extracts a phone number from text, prioritizing Indian numbers,
    and falls back to a generic pattern. Returns cleaned digits or "N/A".
    """
    if not isinstance(text, str):
        return "N/A"

    # Prefer Indian phone numbers (covers +91 or not, 6-9 followed by 9 digits)
    pattern_india = re.compile(r'(?:\+91[\-\s]?)?\b[6-9]\d{9}\b')
    match_india = pattern_india.search(text)
    if match_india:
        phone = re.sub(r'\D', '', match_india.group(0))
        if 10 <= len(phone) <= 12:
            # Remove leading 91 if it's a 12-digit number starting with 91
            if len(phone) == 12 and phone.startswith("91"):
                phone = phone[2:]
            if len(phone) == 11 and phone.startswith("0"):
                phone = phone[1:]
            if len(phone) == 10:
                return phone

    # Fallback to generic international phone pattern
    pattern_generic = re.compile(
        r'(?:\+?\d{1,3}[-.\s]?)?'  # Optional country code
        r'(?:\(?\d{2,5}\)?[-.\s]?)?'  # Area code
        r'\d{2,5}[-.\s]?\d{2,5}[-.\s]?\d{0,5}'  # Main number parts
        r'(?:\s*(?:ext|x|Ext|EXT|X)\.?\s*\d{1,5})?'  # Optional extension
    )

    best_phone = "N/A"
    longest_cleaned_len = 0

    for match in pattern_generic.finditer(text):
        potential_phone_text = match.group(0)
        potential_phone_text = re.sub(r'^\D+', '', potential_phone_text)
        cleaned_phone = re.sub(r'\D', '', potential_phone_text)

        if 7 <= len(cleaned_phone) <= 15:
            if len(cleaned_phone) > longest_cleaned_len:
                best_phone = cleaned_phone
                longest_cleaned_len = len(cleaned_phone)
            elif best_phone == "N/A":
                best_phone = cleaned_phone
                longest_cleaned_len = len(cleaned_phone)

    return best_phone if best_phone != "N/A" else "N/A"


def get_cache_key(url: str) -> str:
    """Generate a cache key for a URL."""
    return hashlib.md5(url.encode()).hexdigest()


def get_cached_data(url: str) -> Optional[Dict[str, Any]]:
    """Get cached data for a URL if it exists."""
    cache_key = f"scraper_{get_cache_key(url)}"
    return cache.get(cache_key)


def save_to_cache(url: str, data: Dict[str, Any]) -> None:
    """Save data to cache."""
    cache_key = f"scraper_{get_cache_key(url)}"
    cache.set(cache_key, data, CACHE_TIMEOUT)


async def fetch_duckduckgo(session: aiohttp.ClientSession, query: str) -> List[Dict[str, str]]:
    """Fetch search results from DuckDuckGo."""
    try:
        url = DUCKDUCKGO_BASE.format(quote(query))
        async with session.get(url, headers=HEADERS) as response:
            response.raise_for_status()
            html_text = await response.text()
            soup = BeautifulSoup(html_text, 'html.parser')
            results = soup.find_all('a', class_='result__a')
            
            return [
                {
                    "Result Title": tag.get_text(strip=True),
                    "Result URL": tag.get('href')
                }
                for tag in results
                if tag and tag.get('href')
            ]
    except Exception as e:
        logger.error(f"Error fetching query '{query}': {e}")
        return []


async def scrape_duckduckgo(queries: List[str], max_results_per_query: int = 5) -> List[Dict[str, str]]:
    """Scrape DuckDuckGo search results for multiple queries."""
    all_results = []
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_duckduckgo(session, query) for query in queries]
        query_results = await asyncio.gather(*tasks)

        for result_list in query_results:
            if result_list:
                all_results.extend(result_list[:max_results_per_query])

    return all_results
